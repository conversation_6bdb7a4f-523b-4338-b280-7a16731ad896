#!/usr/bin/env python3
"""
NotePlan Auto-Sync Watcher
Automatically keeps your RAG system updated when notes change.
"""

import os
import sys
import time
import threading
import subprocess
from pathlib import Path

# Try to import watchdog, install if not available
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("📦 Installing watchdog dependency...")
    try:
        # Try uv first (preferred)
        subprocess.run(["uv", "tool", "install", "watchdog"], check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        # Fallback to pip with --user flag to avoid system conflicts
        subprocess.run([sys.executable, "-m", "pip", "install", "--user", "watchdog"], check=True)
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler

# Import our RAG system
sys.path.append(str(Path(__file__).parent))
from noteplan_rag_simple import NotePlanRAG

# Configuration
NOTEPLAN_PATH = "/Users/<USER>/Library/Containers/co.noteplan.noteplan3/Data/Library/Application Support/co.noteplan.noteplan3/Notes"
CHECK_INTERVAL = 30  # seconds between batch processing

class NotePlanHandler(FileSystemEventHandler):
    def __init__(self):
        self.rag = NotePlanRAG()
        self.pending_files = set()
        self.lock = threading.Lock()
        
        # Start background processor
        self.processor_thread = threading.Thread(target=self._process_pending, daemon=True)
        self.processor_thread.start()
        
        print("🎯 NotePlan Auto-Sync started!")
        print(f"📁 Watching: {NOTEPLAN_PATH}")
        print("✨ Your notes will be automatically indexed as you create/edit them")
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix.lower() in ['.md', '.txt']:
            with self.lock:
                self.pending_files.add(str(file_path))
            print(f"📝 Detected change: {file_path.name}")
    
    def on_created(self, event):
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix.lower() in ['.md', '.txt']:
            with self.lock:
                self.pending_files.add(str(file_path))
            print(f"✨ New note created: {file_path.name}")
    
    def on_moved(self, event):
        if event.is_directory:
            return
        
        # Handle renamed files
        dest_path = Path(event.dest_path)
        if dest_path.suffix.lower() in ['.md', '.txt']:
            with self.lock:
                self.pending_files.add(str(dest_path))
            print(f"📋 Note renamed: {dest_path.name}")
    
    def _process_pending(self):
        """Background thread that processes pending files in batches"""
        while True:
            time.sleep(CHECK_INTERVAL)
            
            with self.lock:
                if not self.pending_files:
                    continue
                
                files_to_process = list(self.pending_files)
                self.pending_files.clear()
            
            print(f"⚡ Processing {len(files_to_process)} changed notes...")
            
            success_count = 0
            for file_path in files_to_process:
                try:
                    if os.path.exists(file_path):
                        if self.rag.embed_note(file_path):
                            success_count += 1
                        else:
                            print(f"⚠️  Failed to process: {Path(file_path).name}")
                    else:
                        print(f"🗑️  File deleted: {Path(file_path).name}")
                except Exception as e:
                    print(f"❌ Error processing {Path(file_path).name}: {e}")
            
            if success_count > 0:
                print(f"✅ Successfully updated {success_count} notes in RAG system")

def run_watcher():
    """Run the file system watcher"""
    if not Path(NOTEPLAN_PATH).exists():
        print(f"❌ NotePlan directory not found: {NOTEPLAN_PATH}")
        return
    
    event_handler = NotePlanHandler()
    observer = Observer()
    observer.schedule(event_handler, NOTEPLAN_PATH, recursive=True)
    observer.start()
    
    try:
        print("\n🚀 Auto-sync is running! Press Ctrl+C to stop.")
        print("💡 Tip: Keep this running in the background for automatic updates")
        
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping auto-sync...")
        observer.stop()
        event_handler.rag.close()
    
    observer.join()
    print("✅ Auto-sync stopped")

def run_initial_sync():
    """Run initial full sync of all notes"""
    print("🔄 Running initial sync of all notes...")
    rag = NotePlanRAG()
    try:
        rag.embed_all_notes()
        print("✅ Initial sync complete!")
    finally:
        rag.close()

def main():
    if len(sys.argv) < 2:
        print("""
🤖 NotePlan Auto-Sync Watcher

Usage:
  python noteplan_watcher.py watch        # Start auto-sync watcher
  python noteplan_watcher.py sync         # Run initial full sync
  python noteplan_watcher.py setup        # Run initial sync then start watcher

Examples:
  python noteplan_watcher.py setup        # Recommended first run
  python noteplan_watcher.py watch        # Run in background
        """)
        return
    
    command = sys.argv[1].lower()
    
    if command == "watch":
        run_watcher()
    elif command == "sync":
        run_initial_sync()
    elif command == "setup":
        run_initial_sync()
        print("\n" + "="*50)
        run_watcher()
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    main()
