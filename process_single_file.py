#!/usr/bin/env python3
"""
Process a single NotePlan file with enhanced chunking
"""

import sys
from noteplan_rag_enhanced import EnhancedNotePlanRAG

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 process_single_file.py <file_path>")
        return
    
    file_path = sys.argv[1]
    
    print(f"Processing: {file_path}")
    
    rag = EnhancedNotePlanRAG()
    try:
        result = rag.embed_note(file_path)
        if result:
            print("✅ Successfully processed!")
        else:
            print("❌ Failed to process")
    finally:
        rag.close()

if __name__ == "__main__":
    main()
