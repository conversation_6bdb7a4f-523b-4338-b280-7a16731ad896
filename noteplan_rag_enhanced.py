#!/usr/bin/env python3
"""
NotePlan RAG System - Enhanced Version
Improved chunking and search for better retrieval of specific information
"""

import os
import sys
import sqlite3
import subprocess
import json
import hashlib
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

# Configuration
NOTEPLAN_PATH = "/Users/<USER>/Library/Containers/co.noteplan.noteplan3/Data/Library/Application Support/co.noteplan.noteplan3/Notes"
RAG_DB = "noteplan_rag_enhanced.db"
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
DEFAULT_LLM = "q3"

class EnhancedNotePlanRAG:
    def __init__(self, db_path: str = RAG_DB):
        self.db_path = db_path
        self.conn = None
        self._init_db()
    
    def _init_db(self):
        """Initialize the database with enhanced schema"""
        self.conn = sqlite3.connect(self.db_path)
        
        # Enhanced schema with chunks
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS notes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                note_path TEXT UNIQUE,
                note_title TEXT,
                note_type TEXT,
                content_hash TEXT,
                last_modified TEXT,
                full_content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS chunks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                note_id INTEGER,
                chunk_type TEXT,  -- 'title', 'section', 'paragraph', 'list_item'
                chunk_text TEXT,
                chunk_context TEXT,  -- surrounding context for better understanding
                chunk_order INTEGER,
                embedding_id TEXT,  -- reference to llm embedding
                FOREIGN KEY (note_id) REFERENCES notes (id)
            )
        """)
        
        self.conn.commit()
        print("✅ Enhanced database initialized")
    
    def _chunk_content(self, content: str, title: str) -> List[Dict[str, Any]]:
        """Break content into semantic chunks"""
        chunks = []
        lines = content.split('\n')
        
        # Add title as a chunk
        if title:
            chunks.append({
                'type': 'title',
                'text': title,
                'context': f"Title: {title}",
                'order': 0
            })
        
        current_section = ""
        current_section_title = ""
        paragraph_buffer = []
        chunk_order = 1
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Detect headers
            if line.startswith('#'):
                # Save previous section if exists
                if current_section:
                    chunks.append({
                        'type': 'section',
                        'text': current_section,
                        'context': f"Section '{current_section_title}' in note '{title}'",
                        'order': chunk_order
                    })
                    chunk_order += 1
                
                # Start new section
                current_section_title = re.sub(r'^#+\s*', '', line)
                current_section = line + '\n'
                
                # Also add header as its own chunk
                chunks.append({
                    'type': 'header',
                    'text': current_section_title,
                    'context': f"Header in note '{title}': {current_section_title}",
                    'order': chunk_order
                })
                chunk_order += 1
                
            # Detect list items
            elif re.match(r'^[-*+]\s+', line) or re.match(r'^\d+\.\s+', line):
                # Save paragraph buffer first
                if paragraph_buffer:
                    para_text = ' '.join(paragraph_buffer)
                    if len(para_text) > 20:  # Only save substantial paragraphs
                        chunks.append({
                            'type': 'paragraph',
                            'text': para_text,
                            'context': f"Paragraph in section '{current_section_title}' of note '{title}'",
                            'order': chunk_order
                        })
                        chunk_order += 1
                    paragraph_buffer = []
                
                # Add list item
                list_text = re.sub(r'^[-*+]\s+|^\d+\.\s+', '', line)
                chunks.append({
                    'type': 'list_item',
                    'text': list_text,
                    'context': f"List item in section '{current_section_title}' of note '{title}': {list_text}",
                    'order': chunk_order
                })
                chunk_order += 1
                current_section += line + '\n'
                
            else:
                # Regular paragraph text
                paragraph_buffer.append(line)
                current_section += line + '\n'
                
                # If paragraph gets long, save it as a chunk
                if len(' '.join(paragraph_buffer)) > 200:
                    para_text = ' '.join(paragraph_buffer)
                    chunks.append({
                        'type': 'paragraph',
                        'text': para_text,
                        'context': f"Paragraph in section '{current_section_title}' of note '{title}'",
                        'order': chunk_order
                    })
                    chunk_order += 1
                    paragraph_buffer = []
        
        # Save final section and paragraph
        if current_section:
            chunks.append({
                'type': 'section',
                'text': current_section,
                'context': f"Section '{current_section_title}' in note '{title}'",
                'order': chunk_order
            })
            chunk_order += 1
        
        if paragraph_buffer:
            para_text = ' '.join(paragraph_buffer)
            if len(para_text) > 20:
                chunks.append({
                    'type': 'paragraph',
                    'text': para_text,
                    'context': f"Paragraph in section '{current_section_title}' of note '{title}'",
                    'order': chunk_order
                })
        
        return chunks
    
    def _get_content_hash(self, content: str) -> str:
        """Get hash of content for change detection"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def _extract_title(self, content: str, file_path: str) -> str:
        """Extract title from note content or use filename"""
        lines = content.strip().split('\n')
        
        for line in lines[:5]:
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
            elif line.startswith('## '):
                return line[3:].strip()
        
        return Path(file_path).stem
    
    def embed_note(self, file_path: str) -> bool:
        """Process and embed a single note with enhanced chunking"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ Failed to read {file_path}: {e}")
            return False
        
        if not content.strip():
            return True
        
        # Get metadata
        path_obj = Path(file_path)
        relative_path = str(path_obj.relative_to(NOTEPLAN_PATH))
        note_type = path_obj.suffix[1:] if path_obj.suffix else 'txt'
        content_hash = self._get_content_hash(content)
        title = self._extract_title(content, file_path)
        last_modified = str(path_obj.stat().st_mtime)
        
        # Check if already processed and unchanged
        cursor = self.conn.execute(
            "SELECT id, content_hash FROM notes WHERE note_path = ?",
            (relative_path,)
        )
        existing = cursor.fetchone()
        
        if existing and existing[1] == content_hash:
            return True  # Already up to date
        
        # Delete existing data if it exists
        if existing:
            note_id = existing[0]
            self.conn.execute("DELETE FROM chunks WHERE note_id = ?", (note_id,))
            self.conn.execute("DELETE FROM notes WHERE id = ?", (note_id,))
        
        # Insert note
        cursor = self.conn.execute("""
            INSERT INTO notes(note_path, note_title, note_type, content_hash, last_modified, full_content)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (relative_path, title, note_type, content_hash, last_modified, content))
        
        note_id = cursor.lastrowid
        
        # Create and embed chunks
        chunks = self._chunk_content(content, title)

        # Batch embed all chunks for this note
        self._batch_embed_chunks(note_id, chunks)
        
        self.conn.commit()
        return True

    def _batch_embed_chunks(self, note_id: int, chunks: List[Dict[str, Any]]) -> None:
        """Batch embed chunks for better performance using llm embed-multi"""
        if not chunks:
            return

        print(f"   📦 Batch embedding {len(chunks)} chunks...")

        # Prepare all chunks for embedding
        chunk_data = []
        for chunk in chunks:
            chunk_text = chunk['text']
            embedding_id = f"note_{note_id}_chunk_{chunk['order']}_{chunk['type']}"
            embed_text = f"{chunk['context']}\n\n{chunk_text}"

            chunk_data.append({
                'embedding_id': embedding_id,
                'embed_text': embed_text,
                'chunk': chunk
            })

        # Create a temporary JSONL file for batch embedding
        import tempfile
        import json
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
            for data in chunk_data:
                f.write(json.dumps({
                    'id': data['embedding_id'],
                    'content': data['embed_text']
                }) + '\n')
            temp_file = f.name

        try:
            # Batch embed using llm embed-multi
            cmd = [
                "llm", "embed-multi",
                "chunks",
                temp_file,
                "-m", EMBEDDING_MODEL,
                "-d", self.db_path.replace('.db', '_embeddings.db'),
                "--store",
                "--format", "nl"
            ]

            result = subprocess.run(cmd, check=True, capture_output=True, text=True)

            # Store all chunk metadata
            for data in chunk_data:
                chunk = data['chunk']
                self.conn.execute("""
                    INSERT INTO chunks(note_id, chunk_type, chunk_text, chunk_context, chunk_order, embedding_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (note_id, chunk['type'], chunk['text'], chunk['context'], chunk['order'], data['embedding_id']))

            print(f"   ✅ Successfully batch embedded {len(chunks)} chunks")

        except subprocess.CalledProcessError as e:
            print(f"⚠️  Batch embedding failed, falling back to individual chunks: {e}")
            # Fallback to individual embedding
            self._individual_embed_chunks(note_id, chunks)
        finally:
            # Clean up temp file
            import os
            try:
                os.unlink(temp_file)
            except:
                pass

    def _individual_embed_chunks(self, note_id: int, chunks: List[Dict[str, Any]]) -> None:
        """Fallback method for individual chunk embedding"""
        for chunk in chunks:
            chunk_text = chunk['text']
            embedding_id = f"note_{note_id}_chunk_{chunk['order']}_{chunk['type']}"
            embed_text = f"{chunk['context']}\n\n{chunk_text}"

            cmd = [
                "llm", "embed",
                "-m", EMBEDDING_MODEL,
                "-d", self.db_path.replace('.db', '_embeddings.db'),
                "--store",
                "-c", embed_text,
                "chunks", embedding_id
            ]

            try:
                subprocess.run(cmd, check=True, capture_output=True)

                # Store chunk metadata
                self.conn.execute("""
                    INSERT INTO chunks(note_id, chunk_type, chunk_text, chunk_context, chunk_order, embedding_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (note_id, chunk['type'], chunk_text, chunk['context'], chunk['order'], embedding_id))

            except subprocess.CalledProcessError as e:
                print(f"⚠️  Failed to embed chunk {embedding_id}: {e}")
                continue

    def search_chunks(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for relevant chunks using vector similarity"""
        print(f"🔍 Searching chunks for: '{query}'")
        
        # Search embeddings
        cmd = [
            "llm", "similar", "chunks",
            "-d", self.db_path.replace('.db', '_embeddings.db'),
            "-c", query,
            "-n", str(limit)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            similar_results = []
            if result.stdout.strip():
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        try:
                            similar_results.append(json.loads(line))
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return []
        
        # Get chunk details from database
        results = []
        for item in similar_results:
            embedding_id = item.get('id', '')
            
            cursor = self.conn.execute("""
                SELECT c.chunk_type, c.chunk_text, c.chunk_context, c.chunk_order,
                       n.note_path, n.note_title, n.note_type
                FROM chunks c
                JOIN notes n ON c.note_id = n.id
                WHERE c.embedding_id = ?
            """, (embedding_id,))
            
            chunk_data = cursor.fetchone()
            if chunk_data:
                results.append({
                    'chunk_type': chunk_data[0],
                    'chunk_text': chunk_data[1],
                    'chunk_context': chunk_data[2],
                    'chunk_order': chunk_data[3],
                    'note_path': chunk_data[4],
                    'note_title': chunk_data[5],
                    'note_type': chunk_data[6],
                    'score': item.get('score', 0)
                })
        
        return results
    
    def ask_with_context(self, question: str, model: str = DEFAULT_LLM, limit: int = 5) -> str:
        """Ask a question with relevant chunk context"""
        print(f"🤔 Question: {question}")
        
        # Get relevant chunks
        results = self.search_chunks(question, limit)
        
        if not results:
            print("⚠️  No relevant context found, asking without RAG...")
            prompt = question
        else:
            print(f"📚 Found {len(results)} relevant chunks from your notes!")
            
            # Build context from chunks
            context_parts = []
            for i, result in enumerate(results, 1):
                context_parts.append(f"""
Chunk {i} ({result['chunk_type']} from {result['note_title']}):
{result['chunk_text']}
""")
            
            context = "\n".join(context_parts)
            
            prompt = f"""Based on the following context from my personal NotePlan notes, please answer this question: {question}

Context from my notes:
{context}

Please provide a helpful answer based on this context. If the context doesn't contain relevant information, please say so and provide general guidance."""
        
        # Ask the LLM
        cmd = ["llm", "-m", model, prompt]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"❌ LLM query failed: {e}")
            return ""
    
    def embed_all_notes(self) -> bool:
        """Embed all notes with enhanced chunking"""
        print("🔍 Scanning NotePlan directory...")
        
        notes_path = Path(NOTEPLAN_PATH)
        if not notes_path.exists():
            print(f"❌ NotePlan directory not found: {NOTEPLAN_PATH}")
            return False
        
        note_files = []
        for ext in ['*.md', '*.txt']:
            note_files.extend(notes_path.rglob(ext))
        
        print(f"📝 Found {len(note_files)} notes to process...")
        
        success_count = 0
        for i, note_file in enumerate(note_files, 1):
            relative_path = note_file.relative_to(notes_path)
            print(f"⚡ Processing {i}/{len(note_files)}: {relative_path}")
            
            if self.embed_note(str(note_file)):
                success_count += 1
            else:
                print(f"⚠️  Failed to process {relative_path}")
        
        print(f"✅ Successfully processed {success_count}/{len(note_files)} notes!")
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the processed notes and chunks"""
        cursor = self.conn.execute("""
            SELECT 
                COUNT(DISTINCT n.id) as total_notes,
                COUNT(c.id) as total_chunks,
                c.chunk_type,
                COUNT(*) as count_by_type
            FROM notes n
            LEFT JOIN chunks c ON n.id = c.note_id
            GROUP BY c.chunk_type
        """)
        
        stats = {"total_notes": 0, "total_chunks": 0, "by_chunk_type": {}}
        for row in cursor.fetchall():
            if row[0]:
                stats["total_notes"] = row[0]
            if row[1]:
                stats["total_chunks"] = row[1]
            if row[2]:
                stats["by_chunk_type"][row[2]] = row[3]
        
        return stats
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

def main():
    if len(sys.argv) < 2:
        print("""
🧠 NotePlan RAG System (Enhanced Version)

Usage:
  python noteplan_rag_enhanced.py embed                    # Process all notes with chunking
  python noteplan_rag_enhanced.py search "query"           # Search chunks
  python noteplan_rag_enhanced.py ask "question"           # Ask with chunk context
  python noteplan_rag_enhanced.py ask "question" r1        # Use specific model
  python noteplan_rag_enhanced.py stats                    # Show database stats

Examples:
  python noteplan_rag_enhanced.py ask "when is young men's high adventure?"
  python noteplan_rag_enhanced.py search "high adventure"
        """)
        return
    
    rag = EnhancedNotePlanRAG()
    
    try:
        command = sys.argv[1].lower()
        
        if command == "embed":
            rag.embed_all_notes()
        
        elif command == "search":
            if len(sys.argv) < 3:
                print("❌ Please provide a search query")
                return
            query = sys.argv[2]
            results = rag.search_chunks(query)
            
            print(f"\n📋 Found {len(results)} relevant chunks:")
            for i, result in enumerate(results, 1):
                print(f"\n{i}. {result['chunk_type'].title()} from {result['note_title']}")
                print(f"   Score: {result['score']:.4f}")
                print(f"   Text: {result['chunk_text'][:200]}...")
        
        elif command == "ask":
            if len(sys.argv) < 3:
                print("❌ Please provide a question")
                return
            question = sys.argv[2]
            model = sys.argv[3] if len(sys.argv) > 3 else DEFAULT_LLM
            
            answer = rag.ask_with_context(question, model)
            print(f"\n🤖 Answer (using {model}):")
            print(answer)
        
        elif command == "stats":
            stats = rag.get_stats()
            print(f"\n📊 Enhanced Database Statistics:")
            print(f"Total notes: {stats['total_notes']}")
            print(f"Total chunks: {stats['total_chunks']}")
            print("By chunk type:")
            for chunk_type, count in stats['by_chunk_type'].items():
                print(f"  {chunk_type}: {count}")
        
        else:
            print(f"❌ Unknown command: {command}")
    
    finally:
        rag.close()

if __name__ == "__main__":
    main()
