#!/usr/bin/env python3
"""
NotePlan RAG System with sqlite-vec
A high-performance Retrieval-Augmented Generation system for your NotePlan notes using local LLMs.
"""

import os
import sys
import sqlite3
import subprocess
import json
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional

# Configuration
NOTEPLAN_PATH = "/Users/<USER>/Library/Containers/co.noteplan.noteplan3/Data/Library/Application Support/co.noteplan.noteplan3/Notes"
RAG_DB = "noteplan_rag_vec.db"
DEFAULT_LLM = "q3"  # Your Qwen3 model alias

class NotePlanRAG:
    def __init__(self, db_path: str = RAG_DB):
        self.db_path = db_path
        self.conn = None
        self._init_db()
    
    def _init_db(self):
        """Initialize the database with sqlite-vec extension"""
        self.conn = sqlite3.connect(self.db_path)

        # Enable loading extensions
        self.conn.enable_load_extension(True)

        # Load sqlite-vec extension
        try:
            import sqlite_vec
            self.conn.load_extension(sqlite_vec.loadable_path())
            print("✅ sqlite-vec extension loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load sqlite-vec: {e}")
            print("💡 Falling back to basic SQLite without vector search")
            return
        
        # Create the vector table
        self.conn.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS note_vectors USING vec0(
                embedding float[384],  -- sentence-transformers/all-MiniLM-L6-v2 dimension
                +note_path TEXT,       -- metadata: file path
                +note_title TEXT,      -- metadata: note title
                +note_type TEXT,       -- metadata: md, txt, etc.
                +content_hash TEXT,    -- metadata: for change detection
                +last_modified TEXT,   -- metadata: timestamp
                +content TEXT          -- metadata: full content for retrieval
            )
        """)
        self.conn.commit()
    
    def _get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using sentence-transformers"""
        cmd = [
            "llm", "embed", 
            "-m", "sentence-transformers/all-MiniLM-L6-v2",
            "-c", text,
            "-f", "json"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            data = json.loads(result.stdout)
            return data["embedding"]
        except Exception as e:
            print(f"❌ Failed to get embedding: {e}")
            return []
    
    def _get_content_hash(self, content: str) -> str:
        """Get hash of content for change detection"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def _extract_title(self, content: str, file_path: str) -> str:
        """Extract title from note content or use filename"""
        lines = content.strip().split('\n')
        
        # Look for markdown title
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
            elif line.startswith('## '):
                return line[3:].strip()
        
        # Use filename as fallback
        return Path(file_path).stem
    
    def embed_note(self, file_path: str) -> bool:
        """Embed a single note file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ Failed to read {file_path}: {e}")
            return False
        
        if not content.strip():
            return True  # Skip empty files
        
        # Get metadata
        path_obj = Path(file_path)
        relative_path = str(path_obj.relative_to(NOTEPLAN_PATH))
        note_type = path_obj.suffix[1:] if path_obj.suffix else 'txt'
        content_hash = self._get_content_hash(content)
        title = self._extract_title(content, file_path)
        last_modified = str(path_obj.stat().st_mtime)
        
        # Check if already embedded and unchanged
        cursor = self.conn.execute(
            "SELECT content_hash FROM note_vectors WHERE note_path = ?",
            (relative_path,)
        )
        existing = cursor.fetchone()
        
        if existing and existing[0] == content_hash:
            return True  # Already up to date
        
        # Get embedding
        embedding = self._get_embedding(content)
        if not embedding:
            return False
        
        # Delete existing entry if it exists
        self.conn.execute("DELETE FROM note_vectors WHERE note_path = ?", (relative_path,))
        
        # Insert new embedding
        self.conn.execute("""
            INSERT INTO note_vectors(
                embedding, note_path, note_title, note_type, 
                content_hash, last_modified, content
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            json.dumps(embedding), relative_path, title, note_type,
            content_hash, last_modified, content
        ))
        
        self.conn.commit()
        return True
    
    def embed_all_notes(self) -> bool:
        """Embed all notes in the NotePlan directory"""
        print("🔍 Scanning NotePlan directory...")
        
        notes_path = Path(NOTEPLAN_PATH)
        if not notes_path.exists():
            print(f"❌ NotePlan directory not found: {NOTEPLAN_PATH}")
            return False
        
        # Find all markdown and text files
        note_files = []
        for ext in ['*.md', '*.txt']:
            note_files.extend(notes_path.rglob(ext))
        
        print(f"📝 Found {len(note_files)} notes to process...")
        
        success_count = 0
        for i, note_file in enumerate(note_files, 1):
            relative_path = note_file.relative_to(notes_path)
            print(f"⚡ Processing {i}/{len(note_files)}: {relative_path}")
            
            if self.embed_note(str(note_file)):
                success_count += 1
            else:
                print(f"⚠️  Failed to embed {relative_path}")
        
        print(f"✅ Successfully embedded {success_count}/{len(note_files)} notes!")
        return True
    
    def search_notes(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant notes using vector similarity"""
        print(f"🔍 Searching for: '{query}'")
        
        # Get query embedding
        query_embedding = self._get_embedding(query)
        if not query_embedding:
            return []
        
        # Search for similar vectors
        cursor = self.conn.execute("""
            SELECT 
                note_path, note_title, note_type, content,
                distance
            FROM note_vectors
            WHERE embedding MATCH ?
            ORDER BY distance
            LIMIT ?
        """, (json.dumps(query_embedding), limit))
        
        results = []
        for row in cursor.fetchall():
            results.append({
                'path': row[0],
                'title': row[1],
                'type': row[2],
                'content': row[3],
                'distance': row[4]
            })
        
        return results
    
    def ask_with_context(self, question: str, model: str = DEFAULT_LLM, limit: int = 3) -> str:
        """Ask a question with relevant note context"""
        print(f"🤔 Question: {question}")
        
        # Get relevant context
        results = self.search_notes(question, limit)
        
        if not results:
            print("⚠️  No relevant context found, asking without RAG...")
            prompt = question
        else:
            print(f"📚 Found {len(results)} relevant notes from your collection!")
            
            # Build context from results
            context_parts = []
            for i, result in enumerate(results, 1):
                context_parts.append(f"""
Note {i}: {result['title']} ({result['path']})
{result['content'][:1000]}{'...' if len(result['content']) > 1000 else ''}
""")
            
            context = "\n".join(context_parts)
            
            prompt = f"""Based on the following context from my personal NotePlan notes, please answer this question: {question}

Context from my notes:
{context}

Please provide a helpful answer based on this context. If the context doesn't contain relevant information, please say so and provide general guidance."""
        
        # Ask the LLM
        cmd = ["llm", "-m", model, prompt]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"❌ LLM query failed: {e}")
            return ""
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the embedded notes"""
        cursor = self.conn.execute("""
            SELECT 
                COUNT(*) as total_notes,
                COUNT(DISTINCT note_type) as note_types,
                note_type,
                COUNT(*) as count_by_type
            FROM note_vectors
            GROUP BY note_type
        """)
        
        stats = {"total_notes": 0, "by_type": {}}
        for row in cursor.fetchall():
            if row[0]:  # total_notes
                stats["total_notes"] = row[0]
            if row[2]:  # note_type
                stats["by_type"][row[2]] = row[3]
        
        return stats
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

def main():
    if len(sys.argv) < 2:
        print("""
🧠 NotePlan RAG System (sqlite-vec powered)

Usage:
  python noteplan_rag_vec.py embed                    # Embed all your notes
  python noteplan_rag_vec.py search "query"           # Search your notes
  python noteplan_rag_vec.py ask "question"           # Ask with RAG context
  python noteplan_rag_vec.py ask "question" r1        # Use specific model
  python noteplan_rag_vec.py stats                    # Show database stats

Examples:
  python noteplan_rag_vec.py ask "What are my thoughts on product strategy?"
  python noteplan_rag_vec.py ask "What did I learn about pricing?" r1
  python noteplan_rag_vec.py search "journey mapping"
        """)
        return
    
    rag = NotePlanRAG()
    
    try:
        command = sys.argv[1].lower()
        
        if command == "embed":
            rag.embed_all_notes()
        
        elif command == "search":
            if len(sys.argv) < 3:
                print("❌ Please provide a search query")
                return
            query = sys.argv[2]
            results = rag.search_notes(query)
            
            print(f"\n📋 Found {len(results)} relevant notes:")
            for i, result in enumerate(results, 1):
                print(f"\n{i}. {result['title']} ({result['path']})")
                print(f"   Distance: {result['distance']:.4f}")
                print(f"   Preview: {result['content'][:200]}...")
        
        elif command == "ask":
            if len(sys.argv) < 3:
                print("❌ Please provide a question")
                return
            question = sys.argv[2]
            model = sys.argv[3] if len(sys.argv) > 3 else DEFAULT_LLM
            
            answer = rag.ask_with_context(question, model)
            print(f"\n🤖 Answer (using {model}):")
            print(answer)
        
        elif command == "stats":
            stats = rag.get_stats()
            print(f"\n📊 Database Statistics:")
            print(f"Total notes: {stats['total_notes']}")
            print("By type:")
            for note_type, count in stats['by_type'].items():
                print(f"  {note_type}: {count}")
        
        else:
            print(f"❌ Unknown command: {command}")
    
    finally:
        rag.close()

if __name__ == "__main__":
    main()
