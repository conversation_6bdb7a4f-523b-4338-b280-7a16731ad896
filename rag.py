#!/usr/bin/env python3
"""
NotePlan RAG - Main Interface
Unified interface for all RAG operations
"""

import sys
import subprocess
from pathlib import Path

def main():
    if len(sys.argv) < 2:
        print("""
🧠 NotePlan RAG System

Usage:
  python rag.py ask "question"              # Ask with RAG context
  python rag.py search "query"              # Search your notes
  python rag.py stats                       # Show database stats
  python rag.py sync                        # Manual sync all notes
  python rag.py watch                       # Start auto-sync watcher
  python rag.py setup                       # Initial setup + start watcher

  # Enhanced chunked search (better for specific details)
  python rag.py enhanced-ask "question"     # Ask with enhanced chunking
  python rag.py enhanced-search "query"     # Search chunks instead of full notes
  python rag.py enhanced-sync               # Sync with enhanced chunking

Examples:
  python rag.py ask "What are my thoughts on product strategy?"
  python rag.py enhanced-ask "when is young men's high adventure?"  # Better for specific details
  python rag.py search "journey mapping"
  python rag.py watch                       # Keep running for auto-updates
        """)
        return
    
    command = sys.argv[1].lower()
    script_dir = Path(__file__).parent
    
    if command in ["ask", "search", "stats"]:
        # Use the simple RAG system
        cmd = [sys.executable, str(script_dir / "noteplan_rag_simple.py")] + sys.argv[1:]
        subprocess.run(cmd)

    elif command == "sync":
        # Manual sync
        cmd = [sys.executable, str(script_dir / "noteplan_rag_simple.py"), "embed"]
        subprocess.run(cmd)

    elif command in ["enhanced-ask", "enhanced-search", "enhanced-stats"]:
        # Use the enhanced RAG system with chunking
        enhanced_command = command.replace("enhanced-", "")
        cmd = [sys.executable, str(script_dir / "noteplan_rag_enhanced.py"), enhanced_command] + sys.argv[2:]
        subprocess.run(cmd)

    elif command == "enhanced-sync":
        # Enhanced sync with chunking
        cmd = [sys.executable, str(script_dir / "noteplan_rag_enhanced.py"), "embed"]
        subprocess.run(cmd)

    elif command in ["watch", "setup"]:
        # Use the watcher
        cmd = [sys.executable, str(script_dir / "noteplan_watcher.py")] + sys.argv[1:]
        subprocess.run(cmd)
    
    else:
        print(f"❌ Unknown command: {command}")
        print("Run 'python rag.py' for help")

if __name__ == "__main__":
    main()
