#!/bin/bash

# NotePlan Auto-Sync Launcher
# This script starts the NotePlan RAG auto-sync in the background

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/../LocalLLM/noteplan_watcher.py"
LOG_FILE="$HOME/.noteplan_rag_sync.log"
PID_FILE="$HOME/.noteplan_rag_sync.pid"

case "$1" in
    start)
        if [ -f "$PID_FILE" ] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
            echo "🔄 NotePlan sync is already running (PID: $(cat "$PID_FILE"))"
            exit 1
        fi
        
        echo "🚀 Starting NotePlan auto-sync..."
        nohup python3 "$PYTHON_SCRIPT" watch > "$LOG_FILE" 2>&1 &
        echo $! > "$PID_FILE"
        echo "✅ NotePlan sync started (PID: $!)"
        echo "📋 Log file: $LOG_FILE"
        ;;
    
    stop)
        if [ -f "$PID_FILE" ]; then
            PID=$(cat "$PID_FILE")
            if kill -0 "$PID" 2>/dev/null; then
                kill "$PID"
                rm -f "$PID_FILE"
                echo "🛑 NotePlan sync stopped"
            else
                echo "⚠️  Process not running, cleaning up PID file"
                rm -f "$PID_FILE"
            fi
        else
            echo "⚠️  NotePlan sync is not running"
        fi
        ;;
    
    status)
        if [ -f "$PID_FILE" ] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
            echo "✅ NotePlan sync is running (PID: $(cat "$PID_FILE"))"
        else
            echo "❌ NotePlan sync is not running"
        fi
        ;;
    
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    
    setup)
        echo "🔧 Setting up NotePlan auto-sync..."
        python3 "$PYTHON_SCRIPT" setup
        ;;
    
    logs)
        if [ -f "$LOG_FILE" ]; then
            tail -f "$LOG_FILE"
        else
            echo "📋 No log file found at $LOG_FILE"
        fi
        ;;
    
    *)
        echo "🤖 NotePlan Auto-Sync Control"
        echo ""
        echo "Usage: $0 {start|stop|restart|status|setup|logs}"
        echo ""
        echo "Commands:"
        echo "  setup   - Run initial sync and start watcher"
        echo "  start   - Start auto-sync in background"
        echo "  stop    - Stop auto-sync"
        echo "  restart - Restart auto-sync"
        echo "  status  - Check if auto-sync is running"
        echo "  logs    - View live logs"
        echo ""
        echo "Examples:"
        echo "  $0 setup    # First time setup"
        echo "  $0 start    # Start background sync"
        echo "  $0 logs     # Watch what's happening"
        ;;
esac
