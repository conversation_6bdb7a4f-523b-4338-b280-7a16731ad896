#!/usr/bin/env python3
"""
NotePlan RAG System
A Retrieval-Augmented Generation system for your NotePlan notes using local LLMs.
"""

import os
import sys
import sqlite3
import subprocess
import json
from pathlib import Path

# Configuration
NOTEPLAN_PATH = "/Users/<USER>/Library/Containers/co.noteplan.noteplan3/Data/Library/Application Support/co.noteplan.noteplan3/Notes"
RAG_DB = "noteplan_rag.db"
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
DEFAULT_LLM = "q3"  # Your Qwen3 model alias

def embed_all_notes():
    """Embed all notes in the NotePlan directory"""
    print("🔍 Scanning NotePlan directory...")
    
    notes_path = Path(NOTEPLAN_PATH)
    if not notes_path.exists():
        print(f"❌ NotePlan directory not found: {NOTEPLAN_PATH}")
        return False
    
    # Find all markdown and text files
    note_files = []
    for ext in ['*.md', '*.txt']:
        note_files.extend(notes_path.rglob(ext))
    
    print(f"📝 Found {len(note_files)} notes to embed...")
    
    for i, note_file in enumerate(note_files, 1):
        # Create a clean ID from the file path
        relative_path = note_file.relative_to(notes_path)
        note_id = str(relative_path).replace('/', '_').replace(' ', '_').replace('.', '_')
        
        print(f"⚡ Embedding {i}/{len(note_files)}: {relative_path}")
        
        # Embed the note
        cmd = [
            "llm", "embed", 
            "-m", EMBEDDING_MODEL,
            "-d", RAG_DB,
            "--store",
            "-i", str(note_file),
            "notes", note_id
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Failed to embed {relative_path}: {e}")
            continue
    
    print("✅ Embedding complete!")
    return True

def search_notes(query, limit=5):
    """Search for relevant notes using similarity"""
    print(f"🔍 Searching for: '{query}'")

    cmd = [
        "llm", "similar", "notes",
        "-d", RAG_DB,
        "-c", query,
        "-n", str(limit)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"❌ Search failed: {e}")
        return ""

def ask_with_context(question, model=DEFAULT_LLM):
    """Ask a question with relevant note context"""
    print(f"🤔 Question: {question}")
    
    # Get relevant context
    context = search_notes(question, limit=3)
    
    if not context:
        print("⚠️  No relevant context found, asking without RAG...")
        prompt = question
    else:
        print("📚 Found relevant context from your notes!")
        prompt = f"""Based on the following context from my personal notes, please answer this question: {question}

Context from my notes:
{context}

Please provide a helpful answer based on this context. If the context doesn't contain relevant information, please say so."""
    
    # Ask the LLM
    cmd = ["llm", "-m", model, prompt]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"❌ LLM query failed: {e}")
        return ""

def main():
    if len(sys.argv) < 2:
        print("""
🧠 NotePlan RAG System

Usage:
  python noteplan_rag.py embed              # Embed all your notes
  python noteplan_rag.py search "query"     # Search your notes
  python noteplan_rag.py ask "question"     # Ask with RAG context
  python noteplan_rag.py ask "question" r1  # Use specific model (r1, q3, etc.)

Examples:
  python noteplan_rag.py ask "What are my thoughts on product strategy?"
  python noteplan_rag.py ask "What did I learn about pricing?" r1
  python noteplan_rag.py search "journey mapping"
        """)
        return
    
    command = sys.argv[1].lower()
    
    if command == "embed":
        embed_all_notes()
    
    elif command == "search":
        if len(sys.argv) < 3:
            print("❌ Please provide a search query")
            return
        query = sys.argv[2]
        results = search_notes(query)
        print("\n📋 Search Results:")
        print(results)
    
    elif command == "ask":
        if len(sys.argv) < 3:
            print("❌ Please provide a question")
            return
        question = sys.argv[2]
        model = sys.argv[3] if len(sys.argv) > 3 else DEFAULT_LLM
        
        answer = ask_with_context(question, model)
        print(f"\n🤖 Answer (using {model}):")
        print(answer)
    
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    main()
